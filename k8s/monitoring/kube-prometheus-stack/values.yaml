grafana:
  persistence:
    enabled: true
    type: pvc
    size: 10Gi
  ingress:
    enabled: true
    ingressClassName: nginx
    hosts:
      - grafana.conversas.ai
    adminPassword: 'Oogf@r7&E715'
  
prometheus:
  prometheusSpec:
    retention: 30d
    retentionSize: 45GiB
    storageSpec:
      volumeClaimTemplate:
        spec:
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi
alertmanager:
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: "gp2"
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 5Gi