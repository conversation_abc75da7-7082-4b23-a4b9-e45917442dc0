# Pacto-AI Infrastructure

Este repositório contém a infraestrutura como código para o projeto Pacto-IA, utilizando CDKTF (Cloud Development Kit for Terraform) para provisionar um cluster GKE no Google Cloud Platform.

## Pré-requisitos

- Node.js 14+
- Terraform 1.0+
- CDKTF CLI
- Google Cloud SDK (gcloud)
- kubectl
- Helm (opcional, para instalação do Ingress Controller)

## Configuração

1. Clone este repositório
2. Instale as dependências:
    ```bash
    npm install
    ```
3. Sintetize o código CDKTF para Terraform:
    ```bash
    cdktf synth
    ```
4. Aplique a infraestrutura:
    ```bash
    cdktf deploy
    ```

## Configuração do Ingress Controller

O Ingress Controller permite gerenciar o acesso externo aos serviços em seu cluster Kubernetes, normalmente via HTTP/HTTPS. Siga os passos abaixo para configurar o NGINX Ingress Controller no seu cluster GKE:

### 1. Configure o acesso ao cluster GKE

Primeiro, configure o acesso ao seu cluster GKE usando o gcloud CLI:

```bash
gcloud container clusters get-credentials conversas-ai-cluster --region southamerica-east1 --project conversas-ai
```

### 2. Instale o NGINX Ingress Controller usando Helm

```bash
# Adicione o repositório Helm do NGINX Ingress Controller e instale no cluster
```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update

helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx --create-namespace \
  --set controller.service.externalTrafficPolicy=Local \
  --set controller.config.use-forwarded-headers="true" \
  --set controller.config.enable-real-ip="true" \
  --set controller.config.proxy-real-ip-cidr="***********/22\,**********/16"
```

### 3. Verifique a instalação

Aguarde alguns minutos para que o LoadBalancer seja provisionado e então verifique se o serviço está disponível:

```bash
kubectl get service -n ingress-nginx ingress-nginx-controller
```

Você deverá ver um IP externo associado ao serviço. Este é o IP que você deve usar para configurar seus registros DNS.

### 4. Obtenha o IP do LoadBalancer

```bash
kubectl get service -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}'
```

### 5. Configure o DNS no Cloudflare

Adicione um registro A no Cloudflare apontando para o IP do LoadBalancer:

- Tipo: A
- Nome: seu-dominio.com (ou subdomínio)
- Conteúdo: [IP do LoadBalancer]
- Proxy status: Proxied (recomendado para proteção adicional)

### 7. Configuração de TLS (opcional)

Recomendamos você usar o certificado de borda do cloudflare, mas se precisar de certificado emitido pelo cluster siga esses passos:
Para habilitar HTTPS interno, você pode configurar o TLS no seu Ingress. usar o cert-manager para gerenciar certificados automaticamente:

```bash
# Instale o cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Crie um ClusterIssuer para Let's Encrypt
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
   name: letsencrypt-prod
spec:
   acme:
     server: https://acme-v02.api.letsencrypt.org/directory
     email: <EMAIL>
     privateKeySecretRef:
       name: letsencrypt-prod
     solvers:
     - http01:
         ingress:
           class: nginx
EOF

# Atualize seu Ingress para usar TLS
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
   name: app-ingress
   namespace: seu-namespace
   annotations:
     kubernetes.io/ingress.class: nginx
     cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
   tls:
   - hosts:
     - seu-dominio.com
     secretName: seu-dominio-tls
   rules:
   - host: seu-dominio.com
     http:
       paths:
       - path: /
         pathType: Prefix
         backend:
           service:
             name: seu-servico
             port:
               number: 80
EOF
```

## Deploy das Aplicações

Após configurar o cluster GKE e o Ingress Controller, siga os passos abaixo para implantar as aplicações do projeto Pacto-IA. Certifique-se de executar os comandos na ordem especificada para garantir que as dependências sejam atendidas corretamente.

### 1. Criar namespaces para as aplicações

```bash
kubectl create namespace conversas-ai
kubectl create namespace redis
kubectl create namespace jaeger
kubectl create namespace monitoring
```

### 2. Deploy do Redis

O Redis é usado como cache e broker de mensagens para as aplicações:

Altere Oogf@r7&E715 para sua senha

```bash
kubectl create secret generic redisinsight-auth --from-literal=auth=$(htpasswd -bn admin "Oogf@r7&E715") -n redis
```

```bash
kubectl apply -f k8s/redis/
```

Verifique se o Redis está em execução:

```bash
kubectl get pods -n redis -l app=redis
```

### 3. Deploy do Jaeger

O Jaeger é usado para rastreamento distribuído:

Altere Oogf@r7&E715 para sua senha

```bash
kubectl create secret generic jaeger-auth --from-literal=auth=$(htpasswd -bn admin "Oogf@r7&E715") -n jaeger
```

```bash
kubectl apply -f k8s/jaeger/
```

Verifique se o Jaeger está em execução:

```bash
kubectl get pods -n jaeger -l app=jaeger
```

### 4. Deploy da API Conversas-AI

A API principal do sistema:

```bash
kubectl apply -f k8s/conversas-ai/gitlab-registry-secret.yaml
kubectl apply -f k8s/conversas-ai/api
```

Verifique se a API está em execução:

```bash
kubectl get pods -n conversas-ai -l app=conversas-ai-api
```

Verifique se o dominio da api está configurado corretamente
```bash
curl https://api.conversas.ai/health/
```

### 5. Deploy do Worker

O worker processa tarefas assíncronas:

```bash
kubectl apply -f k8s/conversas-ai/worker/
```

Verifique se o worker está em execução:

```bash
kubectl get pods -n conversas-ai -l app=worker
```

### 6. Deploy do Monitoring

Sistema de monitoramento da aplicação:

```bash
kubectl apply -f k8s/monitoring/
```

Verifique se o monitoring está em execução:

```bash
kubectl get pods -n conversas-ai -l app=monitoring
```

### 7. Deploy do LLMRouter

O LLMRouter gerencia o roteamento para diferentes modelos de linguagem:

```bash
kubectl apply -f k8s/llmrouter/
```

Verifique se o LLMRouter está em execução:

```bash
kubectl get pods -n conversas-ai -l app=llmrouter
```

### 8. Verificar todos os serviços

Para verificar se todos os serviços estão funcionando corretamente:

```bash
kubectl get pods -n conversas-ai
kubectl get pods -n redis
kubectl get pods -n jaeger
```

Para verificar os serviços expostos:

```bash
kubectl get svc -n conversas-ai
kubectl get svc -n redis
kubectl get svc -n jaeger
```

Para verificar os ingresses configurados:

```bash
kubectl get ingress -n conversas-ai
```

### Instale a stack de monitoramento

# Instala o kube-prometheus-stack com domínio personalizado para o Grafana

helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values k8s/monitoring/kube-prometheus-stack/values.yaml

Importe os graficos customizados do Grafana:

Acesso o Grafana em `https://grafana.conversas.ai` com o usuário `admin` e a senha `Oogf@r7&E715`.
Adicione o datasource para o Loki:
1. Vá para "Configuration" > "Data Sources".
2. Clique em "Add data source".
3. Selecione "Loki".
4. Configure a URL como `http://loki.monitoring.svc.cluster.local:3100`.
5. Clique em "Save & Test".

Importe os dashboards do Grafana:
1. Vá para "Create" > "Import".
2. Selecione os arquivos JSON dos dashboards localizados em `k8s/monitoring/grafana-dashboards/`.

# Instale o Loki stack no namespace monitoring

```bash
helm upgrade --install loki grafana/loki-stack \
  --namespace monitoring \
  -f k8s/monitoring/loki/values.yaml
```

# Instale o Keda para autoscale dos pods

```bash
helm repo add kedacore https://kedacore.github.io/charts
helm repo update
helm install keda kedacore/keda \
  --namespace keda \
  --create-namespace
```

### Verifica a instalação
kubectl get pods -n monitoring

# 4. Deploy do Qdrant

O Qdrant é um banco de dados vetorial usado para armazenar embeddings:

```bash
# Crie o namespace
kubectl create namespace qdrant
```

```bash
# Implante o qdrant
helm repo add qdrant https://qdrant.github.io/qdrant-helm
helm repo update
helm install qdrant qdrant/qdrant --namespace qdrant
```

```bash
# Deina a senha de acesso do dashboard
# Altere Oogf@r7&E715 para sua senha

```bash
kubectl create secret generic qdrant-auth --from-literal=auth=$(htpasswd -bn admin "Oogf@r7&E715") -n qdrant
```


```bash
# Configure o dominio para acesso ao dashboard
kubectl apply -f k8s/qdrant/
```

# Adicione a o agent do gitlab no cluster para configurar deploy no gitlab

Crie um token aqui https://gitlab.com/Plataformazw/ia/orion/-/clusters?tab=all

```bash
helm repo add gitlab https://charts.gitlab.io
helm repo update
helm upgrade --install conversas-ai-cluster gitlab/gitlab-agent \
    --namespace **********************-ai-cluster \
    --create-namespace \
    --set config.token=<seu-token> \
    --set config.kasAddress=wss://kas.gitlab.com
```

Pra conectar no qdrant em outras aplicações utiliza `qdrant.qdrant.svc.cluster.local:6333`

# Adicione o uptime kuma para monitoramento de disponibilidade dos serviços

```bash
kubectl apply -f k8s/uptime/
```

Acesse o uptime kuma em `https://uptime.conversas.ai`

## Solução de problemas

Se você encontrar problemas com o Ingress Controller, verifique os logs:

```bash
kubectl logs -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
```

Para verificar o status dos seus recursos Ingress:

```bash
kubectl get ingress --all-namespaces
kubectl describe ingress nome-do-ingress -n namespace
```

Para verificar logs de uma aplicação específica:

```bash
kubectl logs -n conversas-ai -l app=nome-da-aplicacao
```

Para descrever um pod com problemas:

```bash
kubectl describe pod -n conversas-ai nome-do-pod
```
